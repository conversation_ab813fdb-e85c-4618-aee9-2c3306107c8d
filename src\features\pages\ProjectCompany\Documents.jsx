// Thêm cache và preload ở đầu file
let documentsCache = {};
let documentsCacheTimestamp = {};
const DOCUMENTS_CACHE_DURATION = 10000; // 10 giây
let documentsPreloadPromiseMap = {};

import React, { useState, useEffect } from 'react';
import { getDocuments, deleteDocument, getProjectFiles } from '../../../api/documents';
import '../../../styles/Documents.css';
import downloadIcon from '../../../assets/download.svg';
import searchIcon from '../../../assets/search.svg';
import fileTextIcon from '../../../assets/file-text.svg';
import documentIcon from '../../../assets/document.svg';
import user1Image from '../../../assets/user1.png';
import trashIcon from '../../../assets/trash.svg';
// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import arrangeIcon from '../../../assets/arrange.svg';
import uploadIcon from '../../../assets/upload.svg';
import SortPopup from '../../components/SortPopup'; // Import component SortPopup
import DocumentForm from '../../components/DocumentForm'; // Import DocumentForm component
import DocumentDetail from '../../components/DocumentDetail'; // Import DocumentDetail component
import DeleteConfirmPopup from '../../components/DeleteConfirmPopup'; // Import DeleteConfirmPopup component
import { useParams } from 'react-router-dom';
import { deleteProjectFile } from '../../../api/documents';


const Documents = () => {
  const { projectId } = useParams(); // Lấy projectId động từ URL
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSortPopup, setShowSortPopup] = useState(false);
  const [sortBy, setSortBy] = useState(null);
  const [showDocumentForm, setShowDocumentForm] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  // State cho tooltip người tạo
  const [showCreatorTooltip, setShowCreatorTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [tooltipContent, setTooltipContent] = useState('');

  // Preload function
  const preloadDocuments = async (projectId) => {
    if (documentsPreloadPromiseMap[projectId]) return documentsPreloadPromiseMap[projectId];
    documentsPreloadPromiseMap[projectId] = (async () => {
      try {
        const data = await getProjectFiles(projectId);
        const sortedFiles = data.data.files.sort((a, b) => {
          const dateA = new Date(a.uploadedAt || a.createdAt);
          const dateB = new Date(b.uploadedAt || b.createdAt);
          return dateB - dateA;
        });
        documentsCache[projectId] = sortedFiles;
        documentsCacheTimestamp[projectId] = Date.now();
        return sortedFiles;
      } catch {
        return [];
      }
    })();
    return documentsPreloadPromiseMap[projectId];
  };

  useEffect(() => {
    const fetchFiles = async () => {
      if (!projectId) return;
      const now = Date.now();
      if (documentsCache[projectId] && (now - documentsCacheTimestamp[projectId]) < DOCUMENTS_CACHE_DURATION) {
        setDocuments(documentsCache[projectId]);
        setLoading(false);
        return;
      }
      setLoading(true);
      let sortedFiles = [];
      if (documentsPreloadPromiseMap[projectId]) {
        sortedFiles = await documentsPreloadPromiseMap[projectId];
      } else {
        try {
          const data = await getProjectFiles(projectId);
          sortedFiles = data.data.files.sort((a, b) => {
            const dateA = new Date(a.uploadedAt || a.createdAt);
            const dateB = new Date(b.uploadedAt || b.createdAt);
            return dateB - dateA;
          });
        } catch (error) {
          setLoading(false);
          return;
        }
      }
      setDocuments(sortedFiles);
      documentsCache[projectId] = sortedFiles;
      documentsCacheTimestamp[projectId] = Date.now();
      setLoading(false);
    };
    fetchFiles();
  }, [projectId]);

  useEffect(() => { if (projectId) preloadDocuments(projectId); }, [projectId]);

  // Disable page scroll for this component
  useEffect(() => {
    const dashboardMain = document.querySelector('.dashboard-main');
    if (dashboardMain) {
      dashboardMain.classList.add('no-page-scroll');
    }

    return () => {
      if (dashboardMain) {
        dashboardMain.classList.remove('no-page-scroll');
      }
    };
  }, []);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Hàm toggle popup sắp xếp
  const toggleSortPopup = () => {
    setShowSortPopup(!showSortPopup);
  };

  // Hàm mở form thêm tài liệu
  const handleAddDocument = () => {
    setShowDocumentForm(true);
  };

  // Hàm đóng form thêm tài liệu
  const handleCloseDocumentForm = () => {
    setShowDocumentForm(false);
  };

  // Hàm xử lý submit form thêm tài liệu
  const handleSubmitDocument = async () => {
    try {
      // Đợi một chút để server cập nhật dữ liệu
      await new Promise(resolve => setTimeout(resolve, 500));

      // Refresh lại danh sách tài liệu từ API để đảm bảo có dữ liệu mới nhất
      const data = await getProjectFiles(projectId);

      // Sắp xếp danh sách theo ngày upload mới nhất trước (để tài liệu mới ở đầu)
      const sortedFiles = data.data.files.sort((a, b) => {
        const dateA = new Date(a.uploadedAt || a.createdAt);
        const dateB = new Date(b.uploadedAt || b.createdAt);
        return dateB - dateA; // Mới nhất trước
      });

      setDocuments(sortedFiles);

      // Reset sortBy để đảm bảo không có sắp xếp thủ công nào override
      setSortBy(null);

      // Đã refresh và sắp xếp danh sách tài liệu sau khi thêm mới
    } catch (error) {
      console.error('Lỗi khi refresh danh sách tài liệu:', error);
      // Fallback: thử refresh lại sau 1 giây
      setTimeout(async () => {
        try {
          const data = await getProjectFiles(projectId);
          const sortedFiles = data.data.files.sort((a, b) => {
            const dateA = new Date(a.uploadedAt || a.createdAt);
            const dateB = new Date(b.uploadedAt || b.createdAt);
            return dateB - dateA;
          });
          setDocuments(sortedFiles);
          setSortBy(null);
        } catch (retryError) {
          console.error('Lỗi khi retry refresh:', retryError);
          alert('Có lỗi xảy ra khi cập nhật danh sách tài liệu. Vui lòng refresh trang.');
        }
      }, 1000);
    }
  };

  // Hàm mở chi tiết tài liệu
  const handleDocumentClick = (document) => {
    setSelectedDocument(document);
  };

  // Hàm đóng chi tiết tài liệu
  const handleCloseDocumentDetail = () => {
    setSelectedDocument(null);
  };

  // Hàm mở popup xác nhận xóa tài liệu
  const handleDeleteDocument = (doc) => {
    setDocumentToDelete(doc);
    setShowDeleteConfirm(true);
  };

  // Hàm xác nhận xóa tài liệu
  const confirmDeleteDocument = async () => {
    if (documentToDelete) {
      try {
        // Xóa file tài liệu của project bằng endpoint đúng
        await deleteProjectFile(projectId, documentToDelete._id);
        // Cập nhật state
        setDocuments(prev => prev.filter(doc => doc._id !== documentToDelete._id));
        // Đã xóa tài liệu
        // Đóng chi tiết tài liệu nếu đang mở tài liệu bị xóa
        if (selectedDocument && selectedDocument._id === documentToDelete._id) {
          setSelectedDocument(null);
        }
        // Reset state
        setDocumentToDelete(null);
      } catch (error) {
        console.error('Lỗi khi xóa tài liệu:', error);
        alert('Có lỗi xảy ra khi xóa tài liệu. Vui lòng thử lại.');
      }
    }
  };

  // Hàm đóng popup xác nhận xóa
  const handleCloseDeleteConfirm = () => {
    setShowDeleteConfirm(false);
    setDocumentToDelete(null);
  };

  // Hàm xử lý hover vào avatar người tạo
  const handleCreatorHover = (e, creatorName) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      top: rect.top - 30 + window.scrollY, // Hiện tooltip phía trên avatar
      left: rect.left + (rect.width / 2) + window.scrollX // Căn giữa ngang
    });
    setTooltipContent(creatorName);
    setShowCreatorTooltip(true);
  };

  // Hàm xử lý mouse leave khỏi avatar người tạo
  const handleCreatorLeave = () => {
    setShowCreatorTooltip(false);
  };

  // Hàm xử lý khi chọn tuỳ chọn sắp xếp
  const handleSortOptionSelect = (option) => {
    if (sortBy === option) {
      setSortBy(null); // Bỏ chọn nếu nhấn lại mục đang chọn
    } else {
      setSortBy(option);
    }
    // Không đóng popup ngay lập tức để người dùng có thể thấy thay đổi
    // Sorting by option
  };

  // Hàm sắp xếp danh sách tài liệu
  const sortDocuments = (docs, sortOption) => {
    if (!sortOption) {
      // Mặc định sắp xếp theo ngày upload mới nhất trước
      return [...docs].sort((a, b) => {
        const dateA = new Date(a.uploadedAt || a.createdAt);
        const dateB = new Date(b.uploadedAt || b.createdAt);
        return dateB - dateA;
      });
    }

    const sortedDocs = [...docs];

    switch (sortOption) {
      case 'name-asc':
        sortedDocs.sort((a, b) => {
          // Chuẩn hóa chuỗi để loại bỏ dấu tiếng Việt cho việc so sánh
          const nameA = a.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
          const nameB = b.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
          return nameA.localeCompare(nameB);
        });
        break;
      case 'name-desc':
        sortedDocs.sort((a, b) => {
          // Chuẩn hóa chuỗi để loại bỏ dấu tiếng Việt cho việc so sánh
          const nameA = a.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
          const nameB = b.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
          return nameB.localeCompare(nameA);
        });
        break;
      case 'create-newest':
        sortedDocs.sort((a, b) => {
          const dateA = new Date(a.uploadedAt || a.createdAt);
          const dateB = new Date(b.uploadedAt || b.createdAt);
          if (!dateA || !dateB) return 0;
          return dateB - dateA;
        });
        break;
      case 'create-oldest':
        sortedDocs.sort((a, b) => {
          const dateA = new Date(a.uploadedAt || a.createdAt);
          const dateB = new Date(b.uploadedAt || b.createdAt);
          if (!dateA || !dateB) return 0;
          return dateA - dateB;
        });
        break;
      default:
        // Mặc định sắp xếp theo ngày upload mới nhất trước
        return [...docs].sort((a, b) => {
          const dateA = new Date(a.uploadedAt || a.createdAt);
          const dateB = new Date(b.uploadedAt || b.createdAt);
          return dateB - dateA;
        });
    }

    return sortedDocs;
  };

  const handleDownload = (doc) => {
    // Ưu tiên dùng downloadUrl, fallback sang url nếu có
    const fileUrl = doc.downloadUrl || doc.url;
    if (!fileUrl) {
      alert('Không tìm thấy file để tải về!');
      return;
    }
    // Tạo thẻ a ẩn để tải file
    const link = document.createElement('a');
    link.href = fileUrl;
    // Đặt tên file đúng extension
    link.download = doc.name || 'file';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };



  // Component tooltip người tạo
  const CreatorTooltip = () => {
    if (!showCreatorTooltip) return null;

    return (
      <div
        className="creator-tooltip"
        style={{
          top: `${tooltipPosition.top}px`,
          left: `${tooltipPosition.left}px`,
        }}
      >
        {tooltipContent}
      </div>
    );
  };

  const filteredDocuments = sortDocuments(
    documents.filter(doc =>
      doc.name.toLowerCase().includes(searchTerm.toLowerCase())
    ),
    sortBy
  );

  return (
    <div className="documents-main-content">
            <div className="documents-container">
              <div className="documents-header">
                <div className="documents-title-row">
                  <div className="documents-title">
                    <img src={documentIcon} alt="Documents" className="document-header-icon" />
                    <h1>Tài liệu</h1>
                  </div>
                </div>
                <div className="documents-controls-row">
                  <div className="documents-search">
                    <div className="doc-search-container">
                      <input
                        type="text"
                        placeholder="Tìm kiếm theo tiêu đề tài liệu"
                        value={searchTerm}
                        onChange={handleSearch}
                        className="doc-search-input"
                      />
                      <img src={searchIcon} alt="Search" className="search-icon" />
                    </div>
                  </div>
                  <div className="documents-actions">
                    <div className="sort-container">
                      <button className="btn-sort" onClick={toggleSortPopup}>
                        <img src={arrangeIcon} alt="Sort" className="option-icon" />
                        <span>Sắp xếp</span>
                      </button>
                      {/* Popup sắp xếp sử dụng component SortPopup */}
                      {showSortPopup && (
                        <SortPopup
                          sortBy={sortBy}
                          onSortOptionSelect={handleSortOptionSelect}
                          onClose={() => setShowSortPopup(false)}
                        />
                      )}
                    </div>
                    <button className="add-document-btn" onClick={handleAddDocument}>
                      <img src={uploadIcon} alt="Upload" className="add-icon" />
                      <span>Thêm tài liệu</span>
                    </button>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="documents-table-container">
                  <table className="documents-table">
                    <thead>
                      <tr>
                        <th>Tên tài liệu</th>
                        <th>Ngày tạo</th>
                        <th>Người tạo</th>
                        <th>Tải file</th>
                        <th>Hành động</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.from({ length: 6 }).map((_, idx) => (
                        <tr key={idx} style={{ opacity: 0.7 }}>
                          <td><div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                          <td><div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                          <td><div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                          <td><div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                          <td><div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  <style>{`
                    @keyframes pulse {
                      0% { opacity: 1; }
                      50% { opacity: 0.5; }
                      100% { opacity: 1; }
                    }
                  `}</style>
                </div>
              ) : (
                <div className="documents-table-container">
                  <table className="documents-table">
                    <thead>
                      <tr>
                        <th>Tên tài liệu</th>
                        <th>Ngày tạo</th>
                        <th>Người tạo</th>
                        <th>Tải file</th>
                        <th>Hành động</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredDocuments.length > 0 ? (
                        filteredDocuments.map((doc) => (
                          <tr key={doc._id}>
                            <td className="document-name" onClick={() => handleDocumentClick(doc)} style={{ cursor: 'pointer' }}>
                              <div className="document-info">
                                <img src={fileTextIcon} alt="Document" className="file-text-icon" />
                                <span>{doc.name}</span>
                              </div>
                            </td>
                            <td>
                              {doc.uploadedAt
                                ? new Date(doc.uploadedAt).toLocaleDateString('vi-VN')
                                : 'N/A'}
                            </td>
                            <td className="creator-cell">
                              <div className="creator-info">
                                <img
                                  src={user1Image}
                                  alt="User"
                                  className="creator-avatar"
                                  onMouseEnter={(e) => handleCreatorHover(e, doc.creator)}
                                  onMouseLeave={handleCreatorLeave}
                                />
                              </div>
                            </td>
                            <td>
                              <button
                                className="download-btn"
                                onClick={() => handleDownload(doc)}
                                title={`Tải xuống ${doc.name}`}
                              >
                                <img src={downloadIcon} alt="Download" className="download-icon" />
                              </button>
                            </td>
                            <td>
                              <button
                                className="deletes-btn"
                                onClick={() => handleDeleteDocument(doc)}
                                title={`Xóa ${doc.name}`}
                              >
                                <img src={trashIcon} alt="Delete" className="delete-icon" />
                              </button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="5" className="no-documents">
                            <div className="empty-state">
                              <img src={fileTextIcon} alt="No documents" className="empty-icon" />
                              <p>Không tìm thấy tài liệu nào</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
          </div>

      {/* Document Form */}
      {showDocumentForm && (
        <DocumentForm
          onClose={handleCloseDocumentForm}
          onSubmit={handleSubmitDocument}
        />
      )}

      {/* Document Detail */}
      {selectedDocument && (
        <DocumentDetail
          document={selectedDocument}
          onClose={handleCloseDocumentDetail}
        />
      )}

      {/* Delete Confirmation Popup */}
      <DeleteConfirmPopup
        isOpen={showDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        onConfirm={confirmDeleteDocument}
        documentName={documentToDelete?.name}
        title="Xóa tài liệu"
      />

      {/* Creator Tooltip */}
      <CreatorTooltip />
    </div>
  );
};

export default Documents;
